from django.db.models import Prefetch
from django.utils.timezone import now
from rest_framework.viewsets import GenericViewSet, ModelViewSet
from rest_framework.mixins import CreateModelMixin, RetrieveModelMixin, DestroyModelMixin, UpdateModelMixin
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from .models import Cart, CartItem
from .serializers import (
    CartSerializer, AddCartItemSerializer, UpdateCartItemSerializer, CartItemSerializer,
    ShippingCalculationRequestSerializer, ShippingCalculationResponseSerializer, CartWithShippingSerializer,
    UpdateCartSerializer
)
from ..products.models import AttributeValue, Discount
from apps.shipping.services.on_demand import OnDemandShippingService
import logging

logger = logging.getLogger(__name__)


class CartViewSet(CreateModelMixin,
                  RetrieveModelMixin,
                  UpdateModelMixin,
                  DestroyModelMixin,
                  GenericViewSet):
    # queryset = Cart.objects.prefetch_related(
    #     'cart_items__product',
    #     'cart_items__product__product_variant',
    # ).all()
    serializer_class = CartSerializer

    # def get_queryset(self):
    #     return Cart.objects.prefetch_related(
    #         Prefetch('cart_items', queryset=CartItem.objects.annotate(
    #             item_total=F('quantity') * F('product_variant__price')
    #         ).select_related('product', 'product_variant'))
    #     )

    # Query optimized solution:
    def get_queryset(self):
        return Cart.objects.prefetch_related(
            Prefetch(
                'cart_items',
                queryset=CartItem.objects.select_related('product', 'product_variant')
                .prefetch_related(
                    Prefetch(
                        'product_variant__price_label',
                        queryset=AttributeValue.objects.select_related('attribute')
                    ),
                    'product_variant__product_image',
                    Prefetch(
                        'product_variant__discounts',
                        queryset=Discount.objects.filter(
                            is_active=True,
                            start_date__lte=now(),
                            end_date__gte=now()
                        ),
                        to_attr='active_discounts'
                    )
                )
            )
        )

    def get_serializer_class(self):
        """Return appropriate serializer based on action and cart state"""
        if self.action == 'update' or self.action == 'partial_update':
            return UpdateCartSerializer

        # For retrieve action, check if cart has shipping calculation
        if self.action == 'retrieve':
            try:
                cart = self.get_object()
                # Use CartWithShippingSerializer if shipping has been calculated
                if hasattr(cart, 'last_shipping_calculation') and cart.last_shipping_calculation:
                    return CartWithShippingSerializer
            except:
                # If we can't get the object, fall back to basic serializer
                pass

        return CartSerializer

    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['update', 'partial_update']:
            return [IsAuthenticated()]
        return super().get_permissions()

    def update(self, request, *args, **kwargs):
        """Update cart to assign authenticated customer"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()

        # Check if cart is already assigned to a customer
        if instance.customer is not None:
            return Response(
                {'error': 'Cart is already assigned to a customer'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the customer associated with the authenticated user
        try:
            from apps.customers.models import Customer
            customer = Customer.objects.get(user=request.user)
        except Customer.DoesNotExist:
            return Response(
                {'error': 'Customer profile not found for authenticated user'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Assign customer to cart
        instance.customer = customer
        instance.save()

        # Return updated cart
        serializer = CartSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)


class CartItemViewSet(ModelViewSet):
    http_method_names = ['get', 'post', 'patch', 'delete']

    # Selecting the serializer according to the request method
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return AddCartItemSerializer
        elif self.request.method == 'PATCH':
            return UpdateCartItemSerializer
        return CartItemSerializer

    def get_serializer_context(self):
        return {'cart_id': self.kwargs['cart_pk']}

    def get_queryset(self):
        return CartItem.objects.filter(cart_id=self.kwargs['cart_pk']) \
            .select_related('product', 'product_variant') \
            .prefetch_related(
            'product_variant__price_label',
            'product_variant__product_image',
            Prefetch(
                'product_variant__discounts',
                queryset=Discount.objects.filter(
                    is_active=True,
                    start_date__lte=now(),
                    end_date__gte=now()
                ),
                to_attr='active_discounts'
            )
        )

    def perform_destroy(self, instance):
        """Delete cart item - shipping calculation is now decoupled"""
        super().perform_destroy(instance)

    @action(detail=False, methods=['post'])
    def calculate_shipping(self, request, cart_pk=None):
        """Calculate shipping costs for cart on-demand"""
        try:
            cart = Cart.objects.get(id=cart_pk)

            # Validate request data
            serializer = ShippingCalculationRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    {'error': 'Invalid request data', 'details': serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get destination address
            destination_address = None
            if serializer.validated_data.get('destination_address_id'):
                # Get address from customer's saved addresses
                try:
                    from apps.customers.models import Address
                    address_id = serializer.validated_data['destination_address_id']
                    destination_address = Address.objects.get(
                        id=address_id,
                        customer=cart.customer
                    )
                except Address.DoesNotExist:
                    return Response(
                        {'error': 'Address not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )
            elif serializer.validated_data.get('destination_address'):
                # Use provided address data
                destination_address = serializer.validated_data['destination_address']

            # Calculate shipping using on-demand service
            shipping_service = OnDemandShippingService()
            get_all_options = request.data.get('get_all_options', False)

            result = shipping_service.calculate_shipping_for_cart(
                cart,
                destination_address=destination_address,
                get_all_options=get_all_options
            )

            # Return cart with shipping information
            cart_serializer = CartWithShippingSerializer(cart)
            response_data = {
                'cart': cart_serializer.data,
                'shipping_calculation': result
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Cart.DoesNotExist:
            return Response(
                {'error': 'Cart not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Failed to calculate shipping for cart {cart_pk}: {e}")
            return Response(
                {'error': 'Failed to calculate shipping', 'details': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def shipping_estimate(self, request, cart_pk=None):
        """Get a quick shipping estimate for the cart"""
        try:
            cart = Cart.objects.get(id=cart_pk)

            destination_zone = request.query_params.get('zone', 'domestic')

            shipping_service = OnDemandShippingService()
            estimate = shipping_service.get_shipping_estimate(cart, destination_zone)

            return Response(estimate, status=status.HTTP_200_OK)

        except Cart.DoesNotExist:
            return Response(
                {'error': 'Cart not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Failed to get shipping estimate for cart {cart_pk}: {e}")
            return Response(
                {'error': 'Failed to get shipping estimate', 'details': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
