'use client'

import Alert from '@/src/components/utils/alert/Alert'
import EmptyCart from '@/src/components/utils/empty-cart/EmptyCart'
import { useCart, useShippingCalculation } from '@/src/hooks/cart-hooks'
import { usePaymentOptions } from '@/src/hooks/checkout-hooks'
import { useCustomerDetails } from '@/src/hooks/customer-hooks'
import { useCreateOrder } from '@/src/hooks/order-hooks'
import authStore from '@/src/stores/auth-store'
import cartStore from '@/src/stores/cart-store'
import { PaymentOptionsShape } from '@/src/types/types'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import styles from './PaymentChoice.module.scss'
import ButtonState from '@/src/components/utils/button-state/ButtonState'
import Spinner from '@/src/components/utils/spinner/Spinner'
import PriceSummary from '../../components/price-summary/PriceSummary'
import CartItemsList from '../../components/cart/CartItemsList'

const PaymentChoice = () => {
  const router = useRouter()
  const { isLoggedIn } = authStore()
  const { data: customer } = useCustomerDetails(isLoggedIn)
  const {
    cartId,
    setSelectedPaymentOption,
    selectedAddress,
    selectedPaymentOption,
  } = cartStore()
  const { isPending, error, data } = useCart()

  const { createOrder } = useCreateOrder()
  const payOptions = usePaymentOptions()
  const shippingCalculation = useShippingCalculation()

  // FOR TESTING UI ONLY: Force isPending to true to see loading spinner
  // createOrder.isPending = true

  console.log(payOptions.data)

  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/login')
    }
  }, [isLoggedIn, router])

  const handlePaymentOptionChange = (paymentOption: PaymentOptionsShape) => {
    setSelectedPaymentOption(paymentOption)
  }

  useEffect(() => {
    if (
      payOptions.data &&
      payOptions.data.length > 0 &&
      !selectedPaymentOption
    ) {
      setSelectedPaymentOption(payOptions.data[0])
    }
  }, [payOptions.data, selectedPaymentOption, setSelectedPaymentOption])

  // Calculate shipping when component loads and when selected address changes
  // Calculate shipping when component loads and when selected address changes
  useEffect(() => {
    if (cartId && selectedAddress?.id && data && data.cart_items?.length > 0) {
      // Only calculate if shipping hasn't been calculated yet
      if (!data.shipping_cost && !shippingCalculation.isPending) {
        shippingCalculation.calculateShipping({
          destination_address_id: selectedAddress.id,
          get_all_options: false,
        })
      }
    }
  }, [
    cartId,
    selectedAddress?.id,
    data?.cart_items?.length,
    // data?.shipping_cost, // Remove this from dependencies
    shippingCalculation.isPending, // Add this to prevent concurrent calls
    shippingCalculation.calculateShipping,
  ])

  // useEffect(() => {
  //   if (shippingCalculation.isSuccess && cartId) {
  //     refetch()
  //   }
  // }, [shippingCalculation.isSuccess, cartId, refetch])

  console.log(customer)
  console.log(selectedAddress)
  console.log(selectedPaymentOption)

  const createOrderFn = () => {
    if (
      window.confirm(
        'Payment Method or other order details cannot be changed after placing the order.\n' +
          'Make sure you have selected the correct payment method and other order details before placing the order.\n' +
          'Click OK to place the order or Cancel to go back and make changes.'
      )
    ) {
      if (customer?.id && selectedAddress?.id && selectedPaymentOption?.id) {
        // Check if shipping calculation is required
        if (!data?.shipping_cost) {
          if (shippingCalculation.isPending) {
            alert(
              'Shipping calculation is in progress. Please wait a moment and try again.'
            )
            return
          } else {
            // Calculate shipping first
            shippingCalculation.calculateShipping({
              destination_address_id: selectedAddress.id,
              get_all_options: false,
            })
            alert('Calculating shipping costs, please try again in a moment.')
            return
          }
        }

        // Shipping is calculated, proceed with order creation
        createOrder.mutate(
          {
            cart_id: cartId!,
            delivery_status: 'Pending',
            selected_address: selectedAddress.id,
            payment_method: selectedPaymentOption.id,
          },
          {
            onSuccess: (data) => {
              router.push(`/checkout/order/${data.id}`)
            },
          }
        )
      }
    }
  }

  return (
    <>
      {!cartId ? (
        <EmptyCart message='Your cart is empty. Add some products to the cart to checkout!' />
      ) : isPending ? (
        <Spinner loading={true} size={20} color='#0091CF' spinnerType='clip' />
      ) : error ? (
        <Alert variant='error' message={error.message} />
      ) : !data ||
        data?.cart_items?.length === 0 ||
        Object.keys(data).length === 0 ? (
        <div className={styles.empty_cart}>
          <p>Your cart is empty. Add some products to the cart to checkout!</p>
          <Link href='/'>Go Shopping </Link>
        </div>
      ) : (
        <>
          <div className='container'>
            <h3 className={styles.place_order}>Place Order</h3>
            <div className={styles.payment_options_stage}>
              <section>
                <section className={styles.contact_info}>
                  <div className={styles.contact_details}>
                    <h3>Contact Details: </h3>
                    <p>
                      Deliver to: {customer?.first_name} {customer?.last_name}
                    </p>
                    <p>Phone: {customer?.phone_number}</p>
                    <p>Email to: {customer?.email}</p>
                  </div>
                  <div className={styles.shipping_address}>
                    <h3>Shipping address: </h3>
                    <address>
                      {selectedAddress?.full_name},<br />
                      {selectedAddress?.street_name},<br />
                      {selectedAddress?.postal_code},<br />
                      {selectedAddress?.city_or_village}
                      <br />
                    </address>
                  </div>
                </section>
                <hr />
                <div className={styles.cart}>
                  <CartItemsList cartItems={data.cart_items} />
                </div>
                <hr />

                {/* Shipping calculation status */}
                {shippingCalculation.isPending && (
                  <Alert
                    variant='info'
                    message='Calculating shipping costs...'
                  />
                )}

                {shippingCalculation.error && (
                  <div>
                    <Alert
                      variant='error'
                      message={`Shipping calculation failed: ${shippingCalculation.error.message}`}
                    />
                    <button
                      onClick={() =>
                        selectedAddress?.id &&
                        shippingCalculation.calculateShipping({
                          destination_address_id: selectedAddress.id,
                          get_all_options: false,
                        })
                      }
                      style={{
                        marginTop: '10px',
                        padding: '8px 16px',
                        backgroundColor: '#0091CF',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                      }}
                    >
                      Retry Shipping Calculation
                    </button>
                  </div>
                )}

                {data?.shipping_cost && (
                  <Alert
                    variant='success'
                    message={`Shipping calculated successfully! Estimated delivery: ${
                      data.packing_details?.calculated_at
                        ? new Date(
                            data.packing_details.calculated_at
                          ).toLocaleDateString()
                        : 'N/A'
                    }`}
                  />
                )}

                <section>
                  <div className={styles.payment_options}>
                    <h3>Payment Method:</h3>
                    {payOptions?.isPending ? (
                      <Alert
                        variant='info'
                        message='Payment options are loading'
                      />
                    ) : payOptions?.error ? (
                      <Alert
                        variant='error'
                        message={payOptions.error.message}
                      />
                    ) : payOptions?.data?.length === 0 ? (
                      <Alert
                        variant='error'
                        message='No payment options available'
                      />
                    ) : (
                      <>
                        {payOptions?.data?.map(
                          (option: PaymentOptionsShape) => (
                            <div key={option.id}>
                              <input
                                type='radio'
                                id={`payment-${option.id}`}
                                name='payment-option'
                                checked={
                                  selectedPaymentOption?.id === option.id
                                }
                                onChange={() =>
                                  handlePaymentOptionChange(option)
                                }
                              />
                              <label htmlFor={`payment-${option.id}`}>
                                {option.name}
                              </label>
                            </div>
                          )
                        )}
                      </>
                    )}
                  </div>
                </section>
              </section>
              <section className={styles.price_summary}>
                <PriceSummary
                  totalPrice={data?.total_price}
                  shippingCost={data?.shipping_cost}
                  packingCost={data?.packing_cost}
                  grandTotal={data?.grand_total}
                  item_count={data?.item_count}
                  cart_weight={data?.cart_weight}
                  isShippingCalculated={!!data?.shipping_cost}
                />
                <button
                  type='submit'
                  disabled={
                    createOrder.isPending || shippingCalculation.isPending
                  }
                  onClick={createOrderFn}
                >
                  <ButtonState
                    isLoading={
                      createOrder.isPending || shippingCalculation.isPending
                    }
                    loadingText={
                      shippingCalculation.isPending
                        ? 'Calculating Shipping...'
                        : 'Placing the Order...'
                    }
                    buttonText='Place Order'
                    spinnerSize={16}
                    spinnerColor='#fff'
                    spinnerType='clip'
                  />
                </button>
              </section>
            </div>
          </div>
        </>
      )}
    </>
  )
}

export default PaymentChoice
