@use '../../../../../src/scss/variables' as *;
@use '../../../../../src/scss/mixins' as *;

.filter_options {
  padding: $padding-4;
  background: white;
  border-radius: $border-radius-3;
  
  h3 {
    font-size: $font-size-4;
    font-weight: map-get($font-weight, 'medium');
    color: $primary-dark-text-color;
    margin: 0 0 $padding-3 0;
    padding-bottom: $padding-2;
    border-bottom: 2px solid $sky-lighter-blue;
    
    span {
      color: $primary-blue;
      font-weight: map-get($font-weight, 'bold');
    }
  }
}

.primary_filters {
  margin-bottom: $padding-5;
  
  > div {
    margin-bottom: $padding-5;
    padding-bottom: $padding-4;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }
}

.price_range_sliders {
  margin-top: $padding-3;
  
  > div {
    @include flexbox(space-between, center);
    margin-bottom: $padding-3;
    padding: $padding-2;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: $border-radius-2;
    border: 1px solid #dee2e6;
    
    span {
      font-size: $font-size-2;
      font-weight: map-get($font-weight, 'medium');
      color: $primary-dark-text-color;
      min-width: 35px;
    }
    
    :global(input[type="range"]) {
      flex: 1;
      margin: 0 $padding-2;
      height: 6px;
      border-radius: 3px;
      background: linear-gradient(to right, $primary-blue 0%, $sky-light-blue 100%);
      outline: none;
      -webkit-appearance: none;

      &::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: $primary-blue;
        cursor: pointer;
        border: 2px solid white;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        transition: all 0.2s ease;

        &:hover {
          background: $primary-dark-blue;
          transform: scale(1.1);
        }
      }

      &::-moz-range-thumb {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: $primary-blue;
        cursor: pointer;
        border: 2px solid white;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        transition: all 0.2s ease;

        &:hover {
          background: $primary-dark-blue;
          transform: scale(1.1);
        }
      }
    }
    
    label {
      font-size: $font-size-2;
      font-weight: map-get($font-weight, 'bold');
      color: $primary-blue;
      min-width: 60px;
      text-align: right;
      background: white;
      padding: 2px 6px;
      border-radius: $border-radius-1;
      border: 1px solid $sky-light-blue;
    }
  }
}

.primary_filters,
.attribute_filters {
  > div {
    > div {
      @include flexbox(flex-start, center);
      margin-bottom: $padding-2;
      padding: $padding-1 $padding-2;
      border-radius: $border-radius-2;
      transition: all 0.2s ease;
      cursor: pointer;
      
      &:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        transform: translateX(4px);
      }
      
      :global(input[type="checkbox"]),
      :global(input[type="radio"]) {
        margin-right: $padding-2;
        width: 16px;
        height: 16px;
        cursor: pointer;
        position: relative;

        &:checked {
          accent-color: $primary-blue;
        }

        // Custom styling for better appearance
        &[type="checkbox"] {
          border-radius: $border-radius-1;

          &:checked::after {
            content: '✓';
            position: absolute;
            top: -2px;
            left: 2px;
            color: white;
            font-size: 12px;
            font-weight: bold;
          }
        }

        &[type="radio"] {
          border-radius: 50%;
        }
      }
      
      label {
        font-size: $font-size-2;
        color: $primary-dark-text-color;
        cursor: pointer;
        font-weight: map-get($font-weight, 'regular');
        text-transform: capitalize;
        transition: color 0.2s ease;
        
        &:hover {
          color: $primary-blue;
        }
      }
    }
  }
}

.attribute_filters {
  .filter-section {
    margin-bottom: $padding-4;
    padding: $padding-3;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: $border-radius-2;
    border: 1px solid #e9ecef;
    
    h3 {
      margin-bottom: $padding-3;
      color: $primary-dark-blue;
      font-size: $font-size-3;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

// Mobile responsive adjustments
@media (max-width: $tablet) {
  .filter_options {
    padding: $padding-3;
  }
  
  .price_range_sliders {
    > div {
      flex-direction: column;
      gap: $padding-2;
      
      span {
        align-self: flex-start;
      }
      
      :global(input[type="range"]) {
        width: 100%;
        margin: 0;
      }
      
      label {
        align-self: flex-end;
      }
    }
  }
}

// Loading state
.loading_spinner {
  @include flexbox(center, center);
  padding: $padding-5;
  min-height: 200px;
}

// Error state
.error_message {
  padding: $padding-4;
  text-align: center;
  color: $error-text;
  background: $error-bg;
  border-radius: $border-radius-2;
  margin: $padding-3;
}

// No filters available state
.no_filters {
  @include flexbox(center, center);
  padding: $padding-5;
  color: $primary-lighter-text-color;
  font-style: italic;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: $border-radius-2;
  border: 2px dashed #dee2e6;
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .primary_filters > div > div,
  .attribute_filters > div > div {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
  
  :global(input[type="range"])::-webkit-slider-thumb,
  :global(input[type="range"])::-moz-range-thumb {
    transition: none;

    &:hover {
      transform: none;
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .filter_options {
    border: 2px solid black;
  }
  
  .primary_filters > div,
  .attribute_filters .filter-section {
    border: 1px solid black;
  }
  
  :global(input[type="checkbox"]),
  :global(input[type="radio"]) {
    border: 2px solid black;
  }
}
