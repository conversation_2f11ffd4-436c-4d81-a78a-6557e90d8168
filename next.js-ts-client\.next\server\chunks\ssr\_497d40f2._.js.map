{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/empty-cart/EmptyCart.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"action_button\": \"EmptyCart-module-scss-module__p5kxma__action_button\",\n  \"action_section\": \"EmptyCart-module-scss-module__p5kxma__action_section\",\n  \"content_section\": \"EmptyCart-module-scss-module__p5kxma__content_section\",\n  \"description\": \"EmptyCart-module-scss-module__p5kxma__description\",\n  \"empty_cart\": \"EmptyCart-module-scss-module__p5kxma__empty_cart\",\n  \"heading\": \"EmptyCart-module-scss-module__p5kxma__heading\",\n  \"icon_section\": \"EmptyCart-module-scss-module__p5kxma__icon_section\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/empty-cart/EmptyCart.tsx"], "sourcesContent": ["import Link from 'next/link'\r\nimport { FaShoppingCart } from 'react-icons/fa'\r\nimport styles from './EmptyCart.module.scss'\r\n\r\ninterface Props {\r\n  message?: string\r\n  showIcon?: boolean\r\n  actionText?: string\r\n  actionHref?: string\r\n}\r\n\r\nconst EmptyCart = ({\r\n  message,\r\n  showIcon = true,\r\n  actionText = \"Go Shopping\",\r\n  actionHref = \"/\"\r\n}: Props) => {\r\n  return (\r\n    <div className={styles.empty_cart} role=\"region\" aria-label=\"Empty cart\">\r\n      {showIcon && (\r\n        <div className={styles.icon_section} aria-hidden=\"true\">\r\n          <FaShoppingCart />\r\n        </div>\r\n      )}\r\n\r\n      <div className={styles.content_section}>\r\n        <h2 className={styles.heading}>\r\n          {message ? message : \"Your cart is empty\"}\r\n        </h2>\r\n        <p className={styles.description}>\r\n          Add some products to your cart to get started with your shopping journey.\r\n        </p>\r\n      </div>\r\n\r\n      <div className={styles.action_section}>\r\n        <Link\r\n          href={actionHref}\r\n          className={styles.action_button}\r\n          aria-label={`${actionText} - Browse products to add to your cart`}\r\n        >\r\n          {actionText}\r\n        </Link>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\nexport default EmptyCart"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AASA,MAAM,YAAY,CAAC,EACjB,OAAO,EACP,WAAW,IAAI,EACf,aAAa,aAAa,EAC1B,aAAa,GAAG,EACV;IACN,qBACE,8OAAC;QAAI,WAAW,gLAAM,CAAC,UAAU;QAAE,MAAK;QAAS,cAAW;;YACzD,0BACC,8OAAC;gBAAI,WAAW,gLAAM,CAAC,YAAY;gBAAE,eAAY;0BAC/C,cAAA,8OAAC,gKAAc;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAW,gLAAM,CAAC,eAAe;;kCACpC,8OAAC;wBAAG,WAAW,gLAAM,CAAC,OAAO;kCAC1B,UAAU,UAAU;;;;;;kCAEvB,8OAAC;wBAAE,WAAW,gLAAM,CAAC,WAAW;kCAAE;;;;;;;;;;;;0BAKpC,8OAAC;gBAAI,WAAW,gLAAM,CAAC,cAAc;0BACnC,cAAA,8OAAC,uKAAI;oBACH,MAAM;oBACN,WAAW,gLAAM,CAAC,aAAa;oBAC/B,cAAY,GAAG,WAAW,sCAAsC,CAAC;8BAEhE;;;;;;;;;;;;;;;;;AAKX;uCACe", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/stores/order-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { persist } from 'zustand/middleware'\r\n\r\ninterface OrderStoreShape {\r\n  orderId: number | null\r\n  setOrderId: (id: number | null) => void\r\n  // Define other state properties and actions if you uncomment the code for order and customer\r\n  // order: Order;\r\n  // setOrder: (newOrder: Partial<Order>) => void;\r\n  // clearOrder: () => void;\r\n  // customer: Customer;\r\n}\r\n\r\nconst orderStore = create<OrderStoreShape>()(\r\n  persist(\r\n    (set) => ({\r\n      // order: {\r\n      //   orderedProducts: [],\r\n      //   subtotal: 0\r\n      // },\r\n      // setOrder: (newOrder) => set((state) => ({ order: { ...state.order, ...newOrder } })),\r\n      // clearOrder: () => set({ order: { orderedProducts: [], subtotal: 0 } }),\r\n      // customer: {},\r\n      orderId: null,\r\n      setOrderId: (id) => set({ orderId: id }),\r\n    }),\r\n    {\r\n      name: 'order_store',\r\n      // storage: createJSONStorage(() => sessionStorage) // Specify the storage engine (localStorage or sessionStorage)\r\n    }\r\n  )\r\n)\r\n\r\nexport default orderStore\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAYA,MAAM,aAAa,IAAA,kJAAM,IACvB,IAAA,wJAAO,EACL,CAAC,MAAQ,CAAC;QACR,WAAW;QACX,yBAAyB;QACzB,gBAAgB;QAChB,KAAK;QACL,wFAAwF;QACxF,0EAA0E;QAC1E,gBAAgB;QAChB,SAAS;QACT,YAAY,CAAC,KAAO,IAAI;gBAAE,SAAS;YAAG;IACxC,CAAC,GACD;IACE,MAAM;AAER;uCAIW", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/hooks/order-hooks.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'\r\nimport APIClient from '../lib/api-client'\r\nimport { createOrderShape, OrderShape } from '../types/order-types'\r\nimport { CACHE_KEY_ORDER_ITEMS, CACHE_KEY_ORDERS } from '../constants/constants'\r\nimport orderStore from '../stores/order-store'\r\nimport cartStore from '../stores/cart-store'\r\nimport { useState } from 'react'\r\n\r\n\r\nexport const useOrder = (orderId: number) => {\r\n\r\n  const apiClient = new APIClient<OrderShape>(`/orders/${orderId}`)\r\n\r\n  return useQuery({\r\n    // queryKey: [CACHE_KEY_ORDER_ITEMS, orderId],\r\n    queryKey: [CACHE_KEY_ORDER_ITEMS],\r\n    queryFn: () => apiClient.get(),\r\n    staleTime: 0,\r\n  })\r\n\r\n}\r\n\r\nexport const useGetAllOrders = (page: number, queryString: string = '') => {\r\n  const apiClient = new APIClient<OrderShape>(`/orders/`)\r\n  const queryParams = new URLSearchParams(queryString)\r\n\r\n  queryParams.set('page', page.toString())\r\n\r\n  return useQuery({\r\n    queryKey: [CACHE_KEY_ORDERS, page, queryString],\r\n    queryFn: () => apiClient.getAll({ params: Object.fromEntries(queryParams) }),\r\n    // staleTime: 1000 * 60 * 5, // 5 minutes\r\n  })\r\n}\r\n\r\nexport const useCreateOrder = () => {\r\n  // const navigate = useNavigate()\r\n  const { setOrderId } = orderStore()\r\n  const { setCartId } = cartStore()\r\n\r\n  const apiClient = new APIClient<OrderShape, createOrderShape>(`/orders/`)\r\n\r\n  const createOrder = useMutation<OrderShape, Error, createOrderShape>({\r\n    mutationFn: (data) => apiClient.post(data),\r\n    onSuccess: (data) => {\r\n      console.log(data)\r\n      setOrderId(data.id)\r\n      setCartId(null)\r\n      localStorage.removeItem('cart_store')\r\n      if (data.id) {\r\n        // navigate(`/checkout/order/${data.id}/`)\r\n      }\r\n    },\r\n  })\r\n\r\n  return { createOrder }\r\n}\r\n\r\nexport const useDeleteOrder = () => {\r\n  const [isModalOpen, setIsModalOpen] = useState(false)\r\n  const [orderToDelete, setOrderToDelete] = useState<number | null>(null)\r\n  const queryClient = useQueryClient()\r\n  const apiClient = new APIClient(`/orders`)\r\n\r\n  const deleteOrder = useMutation({\r\n    mutationFn: (orderId: number) => apiClient.delete(orderId),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: [CACHE_KEY_ORDERS]\r\n      })\r\n    },\r\n  })\r\n\r\n  const handleDeleteClick = (orderId: number) => {\r\n    setOrderToDelete(orderId)\r\n    setIsModalOpen(true)\r\n  }\r\n\r\n  const confirmDelete = () => {\r\n    if (orderToDelete !== null) {\r\n      deleteOrder.mutate(orderToDelete)\r\n      setIsModalOpen(false)\r\n      setOrderToDelete(null)\r\n    }\r\n  }\r\n\r\n  const cancelDelete = () => {\r\n    setIsModalOpen(false)\r\n    setOrderToDelete(null)\r\n  }\r\n\r\n  return {\r\n    deleteOrder,\r\n    isModalOpen,\r\n    handleDeleteClick,\r\n    confirmDelete,\r\n    cancelDelete\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;;;;;;;AAGO,MAAM,WAAW,CAAC;IAEvB,MAAM,YAAY,IAAI,sIAAS,CAAa,CAAC,QAAQ,EAAE,SAAS;IAEhE,OAAO,IAAA,uLAAQ,EAAC;QACd,8CAA8C;QAC9C,UAAU;YAAC,sJAAqB;SAAC;QACjC,SAAS,IAAM,UAAU,GAAG;QAC5B,WAAW;IACb;AAEF;AAEO,MAAM,kBAAkB,CAAC,MAAc,cAAsB,EAAE;IACpE,MAAM,YAAY,IAAI,sIAAS,CAAa,CAAC,QAAQ,CAAC;IACtD,MAAM,cAAc,IAAI,gBAAgB;IAExC,YAAY,GAAG,CAAC,QAAQ,KAAK,QAAQ;IAErC,OAAO,IAAA,uLAAQ,EAAC;QACd,UAAU;YAAC,iJAAgB;YAAE;YAAM;SAAY;QAC/C,SAAS,IAAM,UAAU,MAAM,CAAC;gBAAE,QAAQ,OAAO,WAAW,CAAC;YAAa;IAE5E;AACF;AAEO,MAAM,iBAAiB;IAC5B,iCAAiC;IACjC,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,0IAAU;IACjC,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,yIAAS;IAE/B,MAAM,YAAY,IAAI,sIAAS,CAA+B,CAAC,QAAQ,CAAC;IAExE,MAAM,cAAc,IAAA,6LAAW,EAAsC;QACnE,YAAY,CAAC,OAAS,UAAU,IAAI,CAAC;QACrC,WAAW,CAAC;YACV,QAAQ,GAAG,CAAC;YACZ,WAAW,KAAK,EAAE;YAClB,UAAU;YACV,aAAa,UAAU,CAAC;YACxB,IAAI,KAAK,EAAE,EAAE;YACX,0CAA0C;YAC5C;QACF;IACF;IAEA,OAAO;QAAE;IAAY;AACvB;AAEO,MAAM,iBAAiB;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAgB;IAClE,MAAM,cAAc,IAAA,wMAAc;IAClC,MAAM,YAAY,IAAI,sIAAS,CAAC,CAAC,OAAO,CAAC;IAEzC,MAAM,cAAc,IAAA,6LAAW,EAAC;QAC9B,YAAY,CAAC,UAAoB,UAAU,MAAM,CAAC;QAClD,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,iJAAgB;iBAAC;YAC9B;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,gBAAgB;QACpB,IAAI,kBAAkB,MAAM;YAC1B,YAAY,MAAM,CAAC;YACnB,eAAe;YACf,iBAAiB;QACnB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,iBAAiB;IACnB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/hooks/checkout-hooks.ts"], "sourcesContent": ["import { useMutation, useQuery } from \"@tanstack/react-query\"\r\nimport APIClient from \"../lib/api-client\"\r\nimport { useOrder } from \"./order-hooks\"\r\nimport { PaymentOptionsShape } from \"../types/types\"\r\n\r\n\r\ninterface ClientSecretShape {\r\n  client_secret: string\r\n}\r\n\r\ninterface CaptureOrderRequest {\r\n  paypal_order_id: string\r\n}\r\n\r\ninterface CaptureOrderResponse {\r\n  status: string\r\n  payment_status: string\r\n}\r\n\r\ninterface PayPalOrderRequest {\r\n  order_id: number\r\n  amount: string\r\n}\r\n\r\ninterface PayPalOrderResponse {\r\n  id: string\r\n  status: string\r\n}\r\n\r\n\r\nexport const useClientSecret = (orderId: number) => {\r\n  const { data: order } = useOrder(orderId)\r\n  const apiClient = new APIClient<ClientSecretShape>(`payments/payment-intent-secret/`)\r\n\r\n  return useQuery({\r\n    queryKey: ['stripeClientSecret', orderId],\r\n    queryFn: () => apiClient.get({\r\n      params: {\r\n        order_id: orderId\r\n      }\r\n    }),\r\n    // If order is not null or undefined and payment method is Stripe, then enable the query\r\n    enabled: !!order && order.payment_method.slug === 'stripe'\r\n  })\r\n}\r\n\r\nexport const usePaymentOptions = () => {\r\n  const apiClient = new APIClient<PaymentOptionsShape[]>('/payments/payment-options/')\r\n\r\n  const payOptions = useQuery({\r\n    queryKey: ['payment_options'],\r\n    queryFn: apiClient.get,\r\n    // keepPreviousData: true,\r\n    // refetchOnWindowFocus: false,\r\n    staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours\r\n    // initialData:  Here we can add categories as static data\r\n  })\r\n\r\n  return payOptions\r\n}\r\n\r\n// PayPal Hooks\r\nexport const useCaptureOrder = () => {\r\n  const apiClient = new APIClient<CaptureOrderResponse, CaptureOrderRequest>('/payments/capture-paypal-order/')\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CaptureOrderRequest) => apiClient.post(data)\r\n  })\r\n}\r\n\r\nexport const useCreatePayPalOrder = ({ orderId, amount }: { orderId: number, amount: number }) => {\r\n  const apiClient = new APIClient<PayPalOrderResponse, PayPalOrderRequest>('payments/create-paypal-order/')\r\n\r\n  return useMutation({\r\n    mutationFn: () => apiClient.post({\r\n      order_id: orderId,\r\n      amount: amount.toString()\r\n    })\r\n  })\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;AACA;AACA;;;;AA4BO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,IAAA,0IAAQ,EAAC;IACjC,MAAM,YAAY,IAAI,sIAAS,CAAoB,CAAC,+BAA+B,CAAC;IAEpF,OAAO,IAAA,uLAAQ,EAAC;QACd,UAAU;YAAC;YAAsB;SAAQ;QACzC,SAAS,IAAM,UAAU,GAAG,CAAC;gBAC3B,QAAQ;oBACN,UAAU;gBACZ;YACF;QACA,wFAAwF;QACxF,SAAS,CAAC,CAAC,SAAS,MAAM,cAAc,CAAC,IAAI,KAAK;IACpD;AACF;AAEO,MAAM,oBAAoB;IAC/B,MAAM,YAAY,IAAI,sIAAS,CAAwB;IAEvD,MAAM,aAAa,IAAA,uLAAQ,EAAC;QAC1B,UAAU;YAAC;SAAkB;QAC7B,SAAS,UAAU,GAAG;QACtB,0BAA0B;QAC1B,+BAA+B;QAC/B,WAAW,KAAK,KAAK,KAAK;IAE5B;IAEA,OAAO;AACT;AAGO,MAAM,kBAAkB;IAC7B,MAAM,YAAY,IAAI,sIAAS,CAA4C;IAE3E,OAAO,IAAA,6LAAW,EAAC;QACjB,YAAY,CAAC,OAA8B,UAAU,IAAI,CAAC;IAC5D;AACF;AAEO,MAAM,uBAAuB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAuC;IAC3F,MAAM,YAAY,IAAI,sIAAS,CAA0C;IAEzE,OAAO,IAAA,6LAAW,EAAC;QACjB,YAAY,IAAM,UAAU,IAAI,CAAC;gBAC/B,UAAU;gBACV,QAAQ,OAAO,QAAQ;YACzB;IACF;AACF", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/checkout/payment-choice/PaymentChoice.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cart\": \"PaymentChoice-module-scss-module__SCceZW__cart\",\n  \"cart_items_quantity\": \"PaymentChoice-module-scss-module__SCceZW__cart_items_quantity\",\n  \"contact_details\": \"PaymentChoice-module-scss-module__SCceZW__contact_details\",\n  \"contact_info\": \"PaymentChoice-module-scss-module__SCceZW__contact_info\",\n  \"logo\": \"PaymentChoice-module-scss-module__SCceZW__logo\",\n  \"payment_options\": \"PaymentChoice-module-scss-module__SCceZW__payment_options\",\n  \"payment_options_stage\": \"PaymentChoice-module-scss-module__SCceZW__payment_options_stage\",\n  \"place_order\": \"PaymentChoice-module-scss-module__SCceZW__place_order\",\n  \"price_summary\": \"PaymentChoice-module-scss-module__SCceZW__price_summary\",\n  \"prices\": \"PaymentChoice-module-scss-module__SCceZW__prices\",\n  \"shipping_address\": \"PaymentChoice-module-scss-module__SCceZW__shipping_address\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/underlay/Underlay.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"underlay\": \"Underlay-module-scss-module__PkWa8a__underlay\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/underlay/Underlay.tsx"], "sourcesContent": ["import React from 'react'\r\nimport styles from './Underlay.module.scss'\r\n\r\ninterface Props {\r\n  children: React.ReactNode\r\n  isOpen: boolean\r\n  onClose?: () => void\r\n  bgOpacity?: number\r\n  // zIndex?: number\r\n}\r\n\r\nconst Underlay = ({ children, isOpen, onClose, bgOpacity = 0.3 }: Props) => {\r\n\r\n  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {\r\n    if (e.target === e.currentTarget && onClose) {\r\n      onClose()\r\n    }\r\n  }\r\n\r\n  return isOpen ? (\r\n    <div\r\n      className={styles.underlay}\r\n      style={{ backgroundColor: `rgba(0, 0, 0, ${bgOpacity})` }}\r\n      onClick={handleOverlayClick}\r\n    >\r\n      {children}\r\n    </div>\r\n  ) : null\r\n}\r\n\r\nexport default Underlay"], "names": [], "mappings": ";;;;;AACA;;;AAUA,MAAM,WAAW,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,GAAG,EAAS;IAErE,MAAM,qBAAqB,CAAC;QAC1B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,SAAS;YAC3C;QACF;IACF;IAEA,OAAO,uBACL,8OAAC;QACC,WAAW,0KAAM,CAAC,QAAQ;QAC1B,OAAO;YAAE,iBAAiB,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAAC;QACxD,SAAS;kBAER;;;;;mDAED;AACN;uCAEe", "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/spinner/Spinner.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport SyncLoader from 'react-spinners/SyncLoader'\r\nimport <PERSON><PERSON><PERSON>oader from 'react-spinners/ClipLoader'\r\nimport PulseLoader from 'react-spinners/PulseLoader'\r\nimport RiseLoader from 'react-spinners/RiseLoader'\r\nimport RotateLoader from 'react-spinners/RotateLoader'\r\nimport ScaleLoader from 'react-spinners/ScaleLoader'\r\nimport CircleLoader from 'react-spinners/CircleLoader'\r\nimport Underlay from '../underlay/Underlay'\r\n\r\ninterface Props {\r\n  loading: boolean\r\n  color?: string\r\n  size?: number\r\n  thickness?: number\r\n  bgOpacity?: number\r\n  useUnderlay?: boolean\r\n  spinnerType?: 'sync' | 'pulse' | 'clip' | 'rise' | 'rotate' | 'scale' | 'circle'\r\n}\r\n\r\nconst Spinner = ({\r\n  loading,\r\n  color,\r\n  size = 150,\r\n  thickness = 5,\r\n  bgOpacity,\r\n  useUnderlay = false,\r\n  spinnerType = 'sync'\r\n}: Props) => {\r\n  const renderSpinner = () => {\r\n    const commonProps = {\r\n      color,\r\n      loading,\r\n      size,\r\n      thickness,\r\n      'aria-label': 'Loading Spinner',\r\n    }\r\n\r\n    switch (spinnerType) {\r\n      case 'clip':\r\n        return <ClipLoader {...commonProps} />\r\n      case 'pulse':\r\n        return <PulseLoader {...commonProps} />\r\n      case 'rise':\r\n        return <RiseLoader {...commonProps} />\r\n      case 'rotate':\r\n        return <RotateLoader {...commonProps} />\r\n      case 'scale':\r\n        return <ScaleLoader {...commonProps} />\r\n      case 'circle':\r\n        return <CircleLoader {...commonProps} />\r\n      case 'sync':\r\n      default:\r\n        return <SyncLoader {...commonProps} />\r\n    }\r\n  }\r\n\r\n  if (!useUnderlay) {\r\n    return loading ? renderSpinner() : null\r\n  }\r\n\r\n  return (\r\n    <Underlay isOpen={loading} bgOpacity={bgOpacity}>\r\n      {renderSpinner()}\r\n    </Underlay>\r\n  )\r\n}\r\n\r\nexport default Spinner"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAqBA,MAAM,UAAU,CAAC,EACf,OAAO,EACP,KAAK,EACL,OAAO,GAAG,EACV,YAAY,CAAC,EACb,SAAS,EACT,cAAc,KAAK,EACnB,cAAc,MAAM,EACd;IACN,MAAM,gBAAgB;QACpB,MAAM,cAAc;YAClB;YACA;YACA;YACA;YACA,cAAc;QAChB;QAEA,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,0JAAU;oBAAE,GAAG,WAAW;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,2JAAW;oBAAE,GAAG,WAAW;;;;;;YACrC,KAAK;gBACH,qBAAO,8OAAC,0JAAU;oBAAE,GAAG,WAAW;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,4JAAY;oBAAE,GAAG,WAAW;;;;;;YACtC,KAAK;gBACH,qBAAO,8OAAC,2JAAW;oBAAE,GAAG,WAAW;;;;;;YACrC,KAAK;gBACH,qBAAO,8OAAC,4JAAY;oBAAE,GAAG,WAAW;;;;;;YACtC,KAAK;YACL;gBACE,qBAAO,8OAAC,0JAAU;oBAAE,GAAG,WAAW;;;;;;QACtC;IACF;IAEA,IAAI,CAAC,aAAa;QAChB,OAAO,UAAU,kBAAkB;IACrC;IAEA,qBACE,8OAAC,8JAAQ;QAAC,QAAQ;QAAS,WAAW;kBACnC;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/button-state/ButtonState.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"loadingSpan\": \"ButtonState-module-scss-module__25_Fyq__loadingSpan\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 478, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/button-state/ButtonState.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Spinner from '@/src/components/utils/spinner/Spinner'\nimport styles from './ButtonState.module.scss'\n\ninterface ButtonStateProps {\n  isLoading: boolean\n  loadingText?: string\n  buttonText: string\n  spinnerSize?: number\n  spinnerType?: 'sync' | 'pulse' | 'clip' | 'rise' | 'rotate' | 'scale' | 'circle'\n  spinnerColor?: string\n  spinnerThickness?: number\n  children?: React.ReactNode\n}\n\nconst ButtonState: React.FC<ButtonStateProps> = ({\n  isLoading,\n  loadingText = 'Loading...',\n  buttonText,\n  spinnerSize = 20,\n  spinnerColor = '#ffffff',\n  spinnerType,\n  spinnerThickness = 2,\n}) => {\n  return (\n    <>\n      {isLoading ? (\n        <span className={styles.loadingSpan}>\n          <Spinner\n            loading={isLoading}\n            size={spinnerSize}\n            color={spinnerColor}\n            spinnerType={spinnerType}\n            thickness={spinnerThickness}\n          />\n          <span>{loadingText}</span>\n        </span>\n      ) : (\n        buttonText\n      )}\n    </>\n  )\n}\n\nexport default ButtonState"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAiBA,MAAM,cAA0C,CAAC,EAC/C,SAAS,EACT,cAAc,YAAY,EAC1B,UAAU,EACV,cAAc,EAAE,EAChB,eAAe,SAAS,EACxB,WAAW,EACX,mBAAmB,CAAC,EACrB;IACC,qBACE;kBACG,0BACC,8OAAC;YAAK,WAAW,oLAAM,CAAC,WAAW;;8BACjC,8OAAC,4JAAO;oBACN,SAAS;oBACT,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,WAAW;;;;;;8BAEb,8OAAC;8BAAM;;;;;;;;;;;uDAGT;;AAIR;uCAEe", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cart__checkout\": \"PriceSummary-module-scss-module__7p8iVa__cart__checkout\",\n  \"total\": \"PriceSummary-module-scss-module__7p8iVa__total\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/tooltip/Tooltip.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"tooltip\": \"Tooltip-module-scss-module__vdbe-W__tooltip\",\n  \"tooltip--bottom\": \"Tooltip-module-scss-module__vdbe-W__tooltip--bottom\",\n  \"tooltip--condition\": \"Tooltip-module-scss-module__vdbe-W__tooltip--condition\",\n  \"tooltip--hover\": \"Tooltip-module-scss-module__vdbe-W__tooltip--hover\",\n  \"tooltip--left\": \"Tooltip-module-scss-module__vdbe-W__tooltip--left\",\n  \"tooltip--right\": \"Tooltip-module-scss-module__vdbe-W__tooltip--right\",\n  \"tooltip--top\": \"Tooltip-module-scss-module__vdbe-W__tooltip--top\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/tooltip/Tooltip.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react'\nimport styles from './Tooltip.module.scss'\n\ninterface TooltipProps {\n  children: ReactNode\n  content: string\n  position?: 'top' | 'bottom' | 'left' | 'right'\n  disabled?: boolean\n  className?: string\n  showOnHover?: boolean\n  showOnCondition?: boolean\n}\n\nconst Tooltip: React.FC<TooltipProps> = ({\n  children,\n  content,\n  position = 'top',\n  disabled = false,\n  className = '',\n  showOnHover = true,\n  showOnCondition = false\n}) => {\n  // Don't show tooltip if disabled or no content\n  if (disabled || !content) {\n    return <>{children}</>\n  }\n\n  const tooltipClasses = [\n    styles.tooltip,\n    styles[`tooltip--${position}`],\n    showOnHover ? styles['tooltip--hover'] : '',\n    showOnCondition ? styles['tooltip--condition'] : '',\n    className\n  ].filter(Boolean).join(' ')\n\n  return (\n    <span className={tooltipClasses} data-tooltip={content}>\n      {children}\n    </span>\n  )\n}\n\nexport default Tooltip\n"], "names": [], "mappings": ";;;;;AACA;;;AAYA,MAAM,UAAkC,CAAC,EACvC,QAAQ,EACR,OAAO,EACP,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,cAAc,IAAI,EAClB,kBAAkB,KAAK,EACxB;IACC,+CAA+C;IAC/C,IAAI,YAAY,CAAC,SAAS;QACxB,qBAAO;sBAAG;;IACZ;IAEA,MAAM,iBAAiB;QACrB,wKAAM,CAAC,OAAO;QACd,wKAAM,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC;QAC9B,cAAc,wKAAM,CAAC,iBAAiB,GAAG;QACzC,kBAAkB,wKAAM,CAAC,qBAAqB,GAAG;QACjD;KACD,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,qBACE,8OAAC;QAAK,WAAW;QAAgB,gBAAc;kBAC5C;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/components/price-summary/PriceSummary.tsx"], "sourcesContent": ["import React from 'react'\r\nimport { RiQuestionFill } from 'react-icons/ri'\r\nimport styles from './PriceSummary.module.scss'\r\nimport Tooltip from '@/src/components/utils/tooltip/Tooltip'\r\n\r\ninterface PriceSummaryProps {\r\n  totalPrice: number\r\n  shippingCost?: number\r\n  packingCost?: number\r\n  grandTotal?: number\r\n  item_count: number\r\n  cart_weight: number\r\n  onCheckout?: () => void\r\n  isShippingCalculated?: boolean\r\n}\r\n\r\nconst PriceSummary: React.FC<PriceSummaryProps> = ({\r\n  totalPrice,\r\n  shippingCost,\r\n  packingCost,\r\n  grandTotal,\r\n  item_count,\r\n  cart_weight,\r\n  onCheckout,\r\n  isShippingCalculated = false,\r\n}) => {\r\n  return (\r\n    <div className={styles.cart__checkout}>\r\n      <div>\r\n        <p>Item count: </p> <p>{item_count}</p>\r\n      </div>\r\n\r\n      <div>\r\n        <p>\r\n          Cart weight:\r\n          <Tooltip\r\n            content={`Shipping cost will be calculated after finalizing adding items to the cart.`}\r\n            position='top'\r\n          >\r\n            <i>\r\n              <RiQuestionFill />\r\n            </i>\r\n          </Tooltip>\r\n        </p>\r\n        <p>{cart_weight}g</p>\r\n      </div>\r\n\r\n      <div>\r\n        <p>Item&apos;s cost: </p> <p>${totalPrice.toFixed(2)}</p>\r\n      </div>\r\n\r\n      {isShippingCalculated && shippingCost !== undefined && (\r\n        <div>\r\n          <p>Shipping cost: </p> <p>${shippingCost.toFixed(2)}</p>\r\n        </div>\r\n      )}\r\n\r\n      {isShippingCalculated && packingCost !== undefined && packingCost > 0 && (\r\n        <div>\r\n          <p>Packing cost: </p> <p>${packingCost.toFixed(2)}</p>\r\n        </div>\r\n      )}\r\n\r\n      {isShippingCalculated && grandTotal !== undefined && (\r\n        <div className={styles.total}>\r\n          <p><strong>Total: </strong></p> <p><strong>${grandTotal.toFixed(2)}</strong></p>\r\n        </div>\r\n      )}\r\n\r\n      {onCheckout && <button onClick={onCheckout}>Proceed to checkout</button>}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default PriceSummary\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAaA,MAAM,eAA4C,CAAC,EACjD,UAAU,EACV,YAAY,EACZ,WAAW,EACX,UAAU,EACV,UAAU,EACV,WAAW,EACX,UAAU,EACV,uBAAuB,KAAK,EAC7B;IACC,qBACE,8OAAC;QAAI,WAAW,oNAAM,CAAC,cAAc;;0BACnC,8OAAC;;kCACC,8OAAC;kCAAE;;;;;;oBAAgB;kCAAC,8OAAC;kCAAG;;;;;;;;;;;;0BAG1B,8OAAC;;kCACC,8OAAC;;4BAAE;0CAED,8OAAC,4JAAO;gCACN,SAAS,CAAC,2EAA2E,CAAC;gCACtF,UAAS;0CAET,cAAA,8OAAC;8CACC,cAAA,8OAAC,gKAAc;;;;;;;;;;;;;;;;;;;;;kCAIrB,8OAAC;;4BAAG;4BAAY;;;;;;;;;;;;;0BAGlB,8OAAC;;kCACC,8OAAC;kCAAE;;;;;;oBAAsB;kCAAC,8OAAC;;4BAAE;4BAAE,WAAW,OAAO,CAAC;;;;;;;;;;;;;YAGnD,wBAAwB,iBAAiB,2BACxC,8OAAC;;kCACC,8OAAC;kCAAE;;;;;;oBAAmB;kCAAC,8OAAC;;4BAAE;4BAAE,aAAa,OAAO,CAAC;;;;;;;;;;;;;YAIpD,wBAAwB,gBAAgB,aAAa,cAAc,mBAClE,8OAAC;;kCACC,8OAAC;kCAAE;;;;;;oBAAkB;kCAAC,8OAAC;;4BAAE;4BAAE,YAAY,OAAO,CAAC;;;;;;;;;;;;;YAIlD,wBAAwB,eAAe,2BACtC,8OAAC;gBAAI,WAAW,oNAAM,CAAC,KAAK;;kCAC1B,8OAAC;kCAAE,cAAA,8OAAC;sCAAO;;;;;;;;;;;oBAAoB;kCAAC,8OAAC;kCAAE,cAAA,8OAAC;;gCAAO;gCAAE,WAAW,OAAO,CAAC;;;;;;;;;;;;;;;;;;YAInE,4BAAc,8OAAC;gBAAO,SAAS;0BAAY;;;;;;;;;;;;AAGlD;uCAEe", "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/TextLimit.tsx"], "sourcesContent": ["interface Props {\r\n  title: string\r\n  maxLength: number\r\n}\r\n\r\nconst LimitTitleLength = ({ title, maxLength }: Props) => {\r\n  function limitTitleLength(title: string, maxLength: number) {\r\n    if (title.length > maxLength) {\r\n      return title.substring(0, maxLength) + '...'\r\n    }\r\n    return title\r\n  }\r\n\r\n  return <>{limitTitleLength(title, maxLength)}</>\r\n}\r\n\r\nexport default LimitTitleLength"], "names": [], "mappings": ";;;;;;AAKA,MAAM,mBAAmB,CAAC,EAAE,KAAK,EAAE,SAAS,EAAS;IACnD,SAAS,iBAAiB,KAAa,EAAE,SAAiB;QACxD,IAAI,MAAM,MAAM,GAAG,WAAW;YAC5B,OAAO,MAAM,SAAS,CAAC,GAAG,aAAa;QACzC;QACA,OAAO;IACT;IAEA,qBAAO;kBAAG,iBAAiB,OAAO;;AACpC;uCAEe", "debugId": null}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cart_item\": \"CartItemsList-module-scss-module__kultEW__cart_item\",\n  \"cart_item__extra_data\": \"CartItemsList-module-scss-module__kultEW__cart_item__extra_data\",\n  \"cart_item__img\": \"CartItemsList-module-scss-module__kultEW__cart_item__img\",\n  \"cart_item__info\": \"CartItemsList-module-scss-module__kultEW__cart_item__info\",\n  \"cart_item__quantity\": \"CartItemsList-module-scss-module__kultEW__cart_item__quantity\",\n  \"cart_item__title\": \"CartItemsList-module-scss-module__kultEW__cart_item__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/components/cart/CartItemsList.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { <PERSON><PERSON><PERSON>, FiMinus, FiTrash2 } from 'react-icons/fi'\r\nimport Link from 'next/link'\r\nimport LimitTitleLength from '@/src/components/utils/TextLimit'\r\nimport { CartItemShape } from '@/src/types/store-types'\r\nimport styles from './CartItemsList.module.scss'\r\n\r\ninterface CartItemsListProps {\r\n  cartItems: CartItemShape[]\r\n  handleIncrement?: (item: CartItemShape) => void\r\n  handleDecrement?: (item: CartItemShape) => void\r\n  deleteCartItem?: (itemId: number) => void\r\n}\r\n\r\nconst CartItemsList = ({\r\n  cartItems,\r\n  handleIncrement,\r\n  handleDecrement,\r\n  deleteCartItem,\r\n}: CartItemsListProps) => {\r\n  return (\r\n    <ul>\r\n      {cartItems?.map((item: CartItemShape) => (\r\n        <li key={item.id} className={styles.cart_item}>\r\n          <div className={styles.cart_item__img}>\r\n            <img\r\n              src={\r\n                item.product_variant?.product_image?.[0]?.image\r\n                  ? `${process.env.NEXT_PUBLIC_CLOUDINARY_URL}/${item.product_variant.product_image[0].image}`\r\n                  : noImagePlaceholder\r\n              }\r\n              alt={\r\n                item.product_variant?.product_image?.[0]?.alternative_text ||\r\n                item.product.title\r\n              }\r\n            />\r\n          </div>\r\n          <div className={styles.cart_item__info}>\r\n            <span className={styles.cart_item__title}>\r\n              <Link href={`/product/${item.product.slug}/`}>\r\n                <LimitTitleLength title={item.product.title} maxLength={60} />\r\n              </Link>\r\n            </span>\r\n            {` `}\r\n            <span>\r\n              (${item.product_variant.price} x {item.quantity})\r\n            </span>\r\n            <span>\r\n              Variant: {item.product_variant?.price_label?.attribute_value}\r\n            </span>\r\n            {Object.entries(item.extra_data).map(([key, value], index) => (\r\n              <div key={index} className={styles.cart_item__extra_data}>\r\n                <p>{key} :</p>\r\n                <p>{value}</p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          <div className={styles.cart_item__quantity}>\r\n            <div>\r\n              <p>Qty:</p>\r\n              {handleDecrement && (\r\n                <button\r\n                  onClick={() => handleDecrement(item)}\r\n                  disabled={item.product_variant.stock_qty === 0}\r\n                >\r\n                  <i>\r\n                    <FiMinus />\r\n                  </i>\r\n                </button>\r\n              )}\r\n              <p>{item.quantity}</p>\r\n              {handleIncrement && (\r\n                <button\r\n                  onClick={() => handleIncrement(item)}\r\n                  disabled={item.product_variant.stock_qty === 0}\r\n                >\r\n                  <i>\r\n                    <FiPlus />\r\n                  </i>\r\n                </button>\r\n              )}\r\n              {deleteCartItem && (\r\n                <button onClick={() => deleteCartItem(item.id)}>\r\n                  <i>\r\n                    <FiTrash2 />\r\n                  </i>\r\n                </button>\r\n              )}\r\n            </div>\r\n            {item.product_variant.stock_qty === 0 && <p>Out of Stock</p>}\r\n          </div>\r\n        </li>\r\n      ))}\r\n    </ul>\r\n  )\r\n}\r\n\r\nexport default CartItemsList\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAeA,MAAM,gBAAgB,CAAC,EACrB,SAAS,EACT,eAAe,EACf,eAAe,EACf,cAAc,EACK;IACnB,qBACE,8OAAC;kBACE,WAAW,IAAI,CAAC,qBACf,8OAAC;gBAAiB,WAAW,yMAAM,CAAC,SAAS;;kCAC3C,8OAAC;wBAAI,WAAW,yMAAM,CAAC,cAAc;kCACnC,cAAA,8OAAC;4BACC,KACE,KAAK,eAAe,EAAE,eAAe,CAAC,EAAE,EAAE,QACtC,yEAA0C,CAAC,EAAE,KAAK,eAAe,CAAC,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,GAC1F;4BAEN,KACE,KAAK,eAAe,EAAE,eAAe,CAAC,EAAE,EAAE,oBAC1C,KAAK,OAAO,CAAC,KAAK;;;;;;;;;;;kCAIxB,8OAAC;wBAAI,WAAW,yMAAM,CAAC,eAAe;;0CACpC,8OAAC;gCAAK,WAAW,yMAAM,CAAC,gBAAgB;0CACtC,cAAA,8OAAC,uKAAI;oCAAC,MAAM,CAAC,SAAS,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;8CAC1C,cAAA,8OAAC,mJAAgB;wCAAC,OAAO,KAAK,OAAO,CAAC,KAAK;wCAAE,WAAW;;;;;;;;;;;;;;;;4BAG3D,CAAC,CAAC,CAAC;0CACJ,8OAAC;;oCAAK;oCACD,KAAK,eAAe,CAAC,KAAK;oCAAC;oCAAI,KAAK,QAAQ;oCAAC;;;;;;;0CAElD,8OAAC;;oCAAK;oCACM,KAAK,eAAe,EAAE,aAAa;;;;;;;4BAE9C,OAAO,OAAO,CAAC,KAAK,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,sBAClD,8OAAC;oCAAgB,WAAW,yMAAM,CAAC,qBAAqB;;sDACtD,8OAAC;;gDAAG;gDAAI;;;;;;;sDACR,8OAAC;sDAAG;;;;;;;mCAFI;;;;;;;;;;;kCAMd,8OAAC;wBAAI,WAAW,yMAAM,CAAC,mBAAmB;;0CACxC,8OAAC;;kDACC,8OAAC;kDAAE;;;;;;oCACF,iCACC,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,UAAU,KAAK,eAAe,CAAC,SAAS,KAAK;kDAE7C,cAAA,8OAAC;sDACC,cAAA,8OAAC,yJAAO;;;;;;;;;;;;;;;kDAId,8OAAC;kDAAG,KAAK,QAAQ;;;;;;oCAChB,iCACC,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,UAAU,KAAK,eAAe,CAAC,SAAS,KAAK;kDAE7C,cAAA,8OAAC;sDACC,cAAA,8OAAC,wJAAM;;;;;;;;;;;;;;;oCAIZ,gCACC,8OAAC;wCAAO,SAAS,IAAM,eAAe,KAAK,EAAE;kDAC3C,cAAA,8OAAC;sDACC,cAAA,8OAAC,0JAAQ;;;;;;;;;;;;;;;;;;;;;4BAKhB,KAAK,eAAe,CAAC,SAAS,KAAK,mBAAK,8OAAC;0CAAE;;;;;;;;;;;;;eAlEvC,KAAK,EAAE;;;;;;;;;;AAwExB;uCAEe", "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/checkout/payment-choice/page.tsx"], "sourcesContent": ["'use client'\n\nimport Alert from '@/src/components/utils/alert/Alert'\nimport EmptyCart from '@/src/components/utils/empty-cart/EmptyCart'\nimport { useCart, useShippingCalculation } from '@/src/hooks/cart-hooks'\nimport { usePaymentOptions } from '@/src/hooks/checkout-hooks'\nimport { useCustomerDetails } from '@/src/hooks/customer-hooks'\nimport { useCreateOrder } from '@/src/hooks/order-hooks'\nimport authStore from '@/src/stores/auth-store'\nimport cartStore from '@/src/stores/cart-store'\nimport { PaymentOptionsShape } from '@/src/types/types'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport styles from './PaymentChoice.module.scss'\nimport ButtonState from '@/src/components/utils/button-state/ButtonState'\nimport Spinner from '@/src/components/utils/spinner/Spinner'\nimport PriceSummary from '../../components/price-summary/PriceSummary'\nimport CartItemsList from '../../components/cart/CartItemsList'\n\nconst PaymentChoice = () => {\n  const router = useRouter()\n  const { isLoggedIn } = authStore()\n  const { data: customer } = useCustomerDetails(isLoggedIn)\n  const {\n    cartId,\n    setSelectedPaymentOption,\n    selectedAddress,\n    selectedPaymentOption,\n  } = cartStore()\n  const { isPending, error, data, refetch } = useCart()\n\n  const { createOrder } = useCreateOrder()\n  const payOptions = usePaymentOptions()\n  const shippingCalculation = useShippingCalculation()\n\n  // FOR TESTING UI ONLY: Force isPending to true to see loading spinner\n  // createOrder.isPending = true\n\n  console.log(payOptions.data)\n\n  useEffect(() => {\n    if (!isLoggedIn) {\n      router.push('/login')\n    }\n  }, [isLoggedIn, router])\n\n  const handlePaymentOptionChange = (paymentOption: PaymentOptionsShape) => {\n    setSelectedPaymentOption(paymentOption)\n  }\n\n  useEffect(() => {\n    if (\n      payOptions.data &&\n      payOptions.data.length > 0 &&\n      !selectedPaymentOption\n    ) {\n      setSelectedPaymentOption(payOptions.data[0])\n    }\n  }, [payOptions.data, selectedPaymentOption, setSelectedPaymentOption])\n\n  // Calculate shipping when component loads and when selected address changes\n  // Calculate shipping when component loads and when selected address changes\n  useEffect(() => {\n    if (cartId && selectedAddress?.id && data && data.cart_items?.length > 0) {\n      // Only calculate if shipping hasn't been calculated yet\n      if (!data.shipping_cost && !shippingCalculation.isPending) {\n        shippingCalculation.calculateShipping({\n          destination_address_id: selectedAddress.id,\n          get_all_options: false,\n        })\n      }\n    }\n  }, [\n    cartId,\n    selectedAddress?.id,\n    data?.cart_items?.length,\n    // data?.shipping_cost, // Remove this from dependencies\n    shippingCalculation.isPending, // Add this to prevent concurrent calls\n    shippingCalculation.calculateShipping,\n  ])\n\n  useEffect(() => {\n    if (shippingCalculation.isSuccess && cartId) {\n      refetch()\n    }\n  }, [shippingCalculation.isSuccess, cartId, refetch])\n\n\n  console.log(customer)\n  console.log(selectedAddress)\n  console.log(selectedPaymentOption)\n\n  const createOrderFn = () => {\n    if (\n      window.confirm(\n        'Payment Method or other order details cannot be changed after placing the order.\\n' +\n        'Make sure you have selected the correct payment method and other order details before placing the order.\\n' +\n        'Click OK to place the order or Cancel to go back and make changes.'\n      )\n    ) {\n      if (customer?.id && selectedAddress?.id && selectedPaymentOption?.id) {\n        // Check if shipping calculation is required\n        if (!data?.shipping_cost) {\n          if (shippingCalculation.isPending) {\n            alert(\n              'Shipping calculation is in progress. Please wait a moment and try again.'\n            )\n            return\n          } else {\n            // Calculate shipping first\n            shippingCalculation.calculateShipping({\n              destination_address_id: selectedAddress.id,\n              get_all_options: false,\n            })\n            alert('Calculating shipping costs, please try again in a moment.')\n            return\n          }\n        }\n\n        // Shipping is calculated, proceed with order creation\n        createOrder.mutate(\n          {\n            cart_id: cartId!,\n            delivery_status: 'Pending',\n            selected_address: selectedAddress.id,\n            payment_method: selectedPaymentOption.id,\n          },\n          {\n            onSuccess: (data) => {\n              router.push(`/checkout/order/${data.id}`)\n            },\n          }\n        )\n      }\n    }\n  }\n\n  return (\n    <>\n      {!cartId ? (\n        <EmptyCart message='Your cart is empty. Add some products to the cart to checkout!' />\n      ) : isPending ? (\n        <Spinner loading={true} size={20} color='#0091CF' spinnerType='clip' />\n      ) : error ? (\n        <Alert variant='error' message={error.message} />\n      ) : !data ||\n        data?.cart_items?.length === 0 ||\n        Object.keys(data).length === 0 ? (\n        <div className={styles.empty_cart}>\n          <p>Your cart is empty. Add some products to the cart to checkout!</p>\n          <Link href='/'>Go Shopping </Link>\n        </div>\n      ) : (\n        <>\n          <div className='container'>\n            <h3 className={styles.place_order}>Place Order</h3>\n            <div className={styles.payment_options_stage}>\n              <section>\n                <section className={styles.contact_info}>\n                  <div className={styles.contact_details}>\n                    <h3>Contact Details: </h3>\n                    <p>\n                      Deliver to: {customer?.first_name} {customer?.last_name}\n                    </p>\n                    <p>Phone: {customer?.phone_number}</p>\n                    <p>Email to: {customer?.email}</p>\n                  </div>\n                  <div className={styles.shipping_address}>\n                    <h3>Shipping address: </h3>\n                    <address>\n                      {selectedAddress?.full_name},<br />\n                      {selectedAddress?.street_name},<br />\n                      {selectedAddress?.postal_code},<br />\n                      {selectedAddress?.city_or_village}\n                      <br />\n                    </address>\n                  </div>\n                </section>\n                <hr />\n                <div className={styles.cart}>\n                  <CartItemsList cartItems={data.cart_items} />\n                </div>\n                <hr />\n\n                {/* Shipping calculation status */}\n                {shippingCalculation.isPending && (\n                  <Alert\n                    variant='info'\n                    message='Calculating shipping costs...'\n                  />\n                )}\n\n                {shippingCalculation.error && (\n                  <div>\n                    <Alert\n                      variant='error'\n                      message={`Shipping calculation failed: ${shippingCalculation.error.message}`}\n                    />\n                    <button\n                      onClick={() =>\n                        selectedAddress?.id &&\n                        shippingCalculation.calculateShipping({\n                          destination_address_id: selectedAddress.id,\n                          get_all_options: false,\n                        })\n                      }\n                      style={{\n                        marginTop: '10px',\n                        padding: '8px 16px',\n                        backgroundColor: '#0091CF',\n                        color: 'white',\n                        border: 'none',\n                        borderRadius: '4px',\n                        cursor: 'pointer',\n                      }}\n                    >\n                      Retry Shipping Calculation\n                    </button>\n                  </div>\n                )}\n\n                {data?.shipping_cost && (\n                  <Alert\n                    variant='success'\n                    message={`Shipping calculated successfully! Estimated delivery: ${data.packing_details?.calculated_at\n                      ? new Date(\n                        data.packing_details.calculated_at\n                      ).toLocaleDateString()\n                      : 'N/A'\n                      }`}\n                  />\n                )}\n\n                <section>\n                  <div className={styles.payment_options}>\n                    <h3>Payment Method:</h3>\n                    {payOptions?.isPending ? (\n                      <Alert\n                        variant='info'\n                        message='Payment options are loading'\n                      />\n                    ) : payOptions?.error ? (\n                      <Alert\n                        variant='error'\n                        message={payOptions.error.message}\n                      />\n                    ) : payOptions?.data?.length === 0 ? (\n                      <Alert\n                        variant='error'\n                        message='No payment options available'\n                      />\n                    ) : (\n                      <>\n                        {payOptions?.data?.map(\n                          (option: PaymentOptionsShape) => (\n                            <div key={option.id}>\n                              <input\n                                type='radio'\n                                id={`payment-${option.id}`}\n                                name='payment-option'\n                                checked={\n                                  selectedPaymentOption?.id === option.id\n                                }\n                                onChange={() =>\n                                  handlePaymentOptionChange(option)\n                                }\n                              />\n                              <label htmlFor={`payment-${option.id}`}>\n                                {option.name}\n                              </label>\n                            </div>\n                          )\n                        )}\n                      </>\n                    )}\n                  </div>\n                </section>\n              </section>\n              <section className={styles.price_summary}>\n                <PriceSummary\n                  totalPrice={data?.total_price}\n                  shippingCost={data?.shipping_cost}\n                  packingCost={data?.packing_cost}\n                  grandTotal={data?.grand_total}\n                  item_count={data?.item_count}\n                  cart_weight={data?.cart_weight}\n                  isShippingCalculated={!!data?.shipping_cost}\n                />\n                <button\n                  type='submit'\n                  disabled={\n                    createOrder.isPending || shippingCalculation.isPending\n                  }\n                  onClick={createOrderFn}\n                >\n                  <ButtonState\n                    isLoading={\n                      createOrder.isPending || shippingCalculation.isPending\n                    }\n                    loadingText={\n                      shippingCalculation.isPending\n                        ? 'Calculating Shipping...'\n                        : 'Placing the Order...'\n                    }\n                    buttonText='Place Order'\n                    spinnerSize={16}\n                    spinnerColor='#fff'\n                    spinnerType='clip'\n                  />\n                </button>\n              </section>\n            </div>\n          </div>\n        </>\n      )}\n    </>\n  )\n}\n\nexport default PaymentChoice\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA;;;;;;;;;;;;;;;;;;AAoBA,MAAM,gBAAgB;IACpB,MAAM,SAAS,IAAA,+IAAS;IACxB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,yIAAS;IAChC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,IAAA,uJAAkB,EAAC;IAC9C,MAAM,EACJ,MAAM,EACN,wBAAwB,EACxB,eAAe,EACf,qBAAqB,EACtB,GAAG,IAAA,yIAAS;IACb,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,wIAAO;IAEnD,MAAM,EAAE,WAAW,EAAE,GAAG,IAAA,gJAAc;IACtC,MAAM,aAAa,IAAA,sJAAiB;IACpC,MAAM,sBAAsB,IAAA,uJAAsB;IAElD,sEAAsE;IACtE,+BAA+B;IAE/B,QAAQ,GAAG,CAAC,WAAW,IAAI;IAE3B,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,YAAY;YACf,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAY;KAAO;IAEvB,MAAM,4BAA4B,CAAC;QACjC,yBAAyB;IAC3B;IAEA,IAAA,kNAAS,EAAC;QACR,IACE,WAAW,IAAI,IACf,WAAW,IAAI,CAAC,MAAM,GAAG,KACzB,CAAC,uBACD;YACA,yBAAyB,WAAW,IAAI,CAAC,EAAE;QAC7C;IACF,GAAG;QAAC,WAAW,IAAI;QAAE;QAAuB;KAAyB;IAErE,4EAA4E;IAC5E,4EAA4E;IAC5E,IAAA,kNAAS,EAAC;QACR,IAAI,UAAU,iBAAiB,MAAM,QAAQ,KAAK,UAAU,EAAE,SAAS,GAAG;YACxE,wDAAwD;YACxD,IAAI,CAAC,KAAK,aAAa,IAAI,CAAC,oBAAoB,SAAS,EAAE;gBACzD,oBAAoB,iBAAiB,CAAC;oBACpC,wBAAwB,gBAAgB,EAAE;oBAC1C,iBAAiB;gBACnB;YACF;QACF;IACF,GAAG;QACD;QACA,iBAAiB;QACjB,MAAM,YAAY;QAClB,wDAAwD;QACxD,oBAAoB,SAAS;QAC7B,oBAAoB,iBAAiB;KACtC;IAED,IAAA,kNAAS,EAAC;QACR,IAAI,oBAAoB,SAAS,IAAI,QAAQ;YAC3C;QACF;IACF,GAAG;QAAC,oBAAoB,SAAS;QAAE;QAAQ;KAAQ;IAGnD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IAEZ,MAAM,gBAAgB;QACpB,IACE,OAAO,OAAO,CACZ,uFACA,+GACA,uEAEF;YACA,IAAI,UAAU,MAAM,iBAAiB,MAAM,uBAAuB,IAAI;gBACpE,4CAA4C;gBAC5C,IAAI,CAAC,MAAM,eAAe;oBACxB,IAAI,oBAAoB,SAAS,EAAE;wBACjC,MACE;wBAEF;oBACF,OAAO;wBACL,2BAA2B;wBAC3B,oBAAoB,iBAAiB,CAAC;4BACpC,wBAAwB,gBAAgB,EAAE;4BAC1C,iBAAiB;wBACnB;wBACA,MAAM;wBACN;oBACF;gBACF;gBAEA,sDAAsD;gBACtD,YAAY,MAAM,CAChB;oBACE,SAAS;oBACT,iBAAiB;oBACjB,kBAAkB,gBAAgB,EAAE;oBACpC,gBAAgB,sBAAsB,EAAE;gBAC1C,GACA;oBACE,WAAW,CAAC;wBACV,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,EAAE,EAAE;oBAC1C;gBACF;YAEJ;QACF;IACF;IAEA,qBACE;kBACG,CAAC,uBACA,8OAAC,oKAAS;YAAC,SAAQ;;;;;uDACjB,0BACF,8OAAC,4JAAO;YAAC,SAAS;YAAM,MAAM;YAAI,OAAM;YAAU,aAAY;;;;;uDAC5D,sBACF,8OAAC,wJAAK;YAAC,SAAQ;YAAQ,SAAS,MAAM,OAAO;;;;;uDAC3C,CAAC,QACH,MAAM,YAAY,WAAW,KAC7B,OAAO,IAAI,CAAC,MAAM,MAAM,KAAK,kBAC7B,8OAAC;YAAI,WAAW,oNAAM,CAAC,UAAU;;8BAC/B,8OAAC;8BAAE;;;;;;8BACH,8OAAC,uKAAI;oBAAC,MAAK;8BAAI;;;;;;;;;;;qEAGjB;sBACE,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAW,oNAAM,CAAC,WAAW;kCAAE;;;;;;kCACnC,8OAAC;wBAAI,WAAW,oNAAM,CAAC,qBAAqB;;0CAC1C,8OAAC;;kDACC,8OAAC;wCAAQ,WAAW,oNAAM,CAAC,YAAY;;0DACrC,8OAAC;gDAAI,WAAW,oNAAM,CAAC,eAAe;;kEACpC,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;;4DAAE;4DACY,UAAU;4DAAW;4DAAE,UAAU;;;;;;;kEAEhD,8OAAC;;4DAAE;4DAAQ,UAAU;;;;;;;kEACrB,8OAAC;;4DAAE;4DAAW,UAAU;;;;;;;;;;;;;0DAE1B,8OAAC;gDAAI,WAAW,oNAAM,CAAC,gBAAgB;;kEACrC,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;;4DACE,iBAAiB;4DAAU;0EAAC,8OAAC;;;;;4DAC7B,iBAAiB;4DAAY;0EAAC,8OAAC;;;;;4DAC/B,iBAAiB;4DAAY;0EAAC,8OAAC;;;;;4DAC/B,iBAAiB;0EAClB,8OAAC;;;;;;;;;;;;;;;;;;;;;;;kDAIP,8OAAC;;;;;kDACD,8OAAC;wCAAI,WAAW,oNAAM,CAAC,IAAI;kDACzB,cAAA,8OAAC,6LAAa;4CAAC,WAAW,KAAK,UAAU;;;;;;;;;;;kDAE3C,8OAAC;;;;;oCAGA,oBAAoB,SAAS,kBAC5B,8OAAC,wJAAK;wCACJ,SAAQ;wCACR,SAAQ;;;;;;oCAIX,oBAAoB,KAAK,kBACxB,8OAAC;;0DACC,8OAAC,wJAAK;gDACJ,SAAQ;gDACR,SAAS,CAAC,6BAA6B,EAAE,oBAAoB,KAAK,CAAC,OAAO,EAAE;;;;;;0DAE9E,8OAAC;gDACC,SAAS,IACP,iBAAiB,MACjB,oBAAoB,iBAAiB,CAAC;wDACpC,wBAAwB,gBAAgB,EAAE;wDAC1C,iBAAiB;oDACnB;gDAEF,OAAO;oDACL,WAAW;oDACX,SAAS;oDACT,iBAAiB;oDACjB,OAAO;oDACP,QAAQ;oDACR,cAAc;oDACd,QAAQ;gDACV;0DACD;;;;;;;;;;;;oCAMJ,MAAM,+BACL,8OAAC,wJAAK;wCACJ,SAAQ;wCACR,SAAS,CAAC,sDAAsD,EAAE,KAAK,eAAe,EAAE,gBACpF,IAAI,KACJ,KAAK,eAAe,CAAC,aAAa,EAClC,kBAAkB,KAClB,OACA;;;;;;kDAIR,8OAAC;kDACC,cAAA,8OAAC;4CAAI,WAAW,oNAAM,CAAC,eAAe;;8DACpC,8OAAC;8DAAG;;;;;;gDACH,YAAY,0BACX,8OAAC,wJAAK;oDACJ,SAAQ;oDACR,SAAQ;;;;;+FAER,YAAY,sBACd,8OAAC,wJAAK;oDACJ,SAAQ;oDACR,SAAS,WAAW,KAAK,CAAC,OAAO;;;;;+FAEjC,YAAY,MAAM,WAAW,kBAC/B,8OAAC,wJAAK;oDACJ,SAAQ;oDACR,SAAQ;;;;;6GAGV;8DACG,YAAY,MAAM,IACjB,CAAC,uBACC,8OAAC;;8EACC,8OAAC;oEACC,MAAK;oEACL,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE;oEAC1B,MAAK;oEACL,SACE,uBAAuB,OAAO,OAAO,EAAE;oEAEzC,UAAU,IACR,0BAA0B;;;;;;8EAG9B,8OAAC;oEAAM,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE;8EACnC,OAAO,IAAI;;;;;;;2DAbN,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAuBjC,8OAAC;gCAAQ,WAAW,oNAAM,CAAC,aAAa;;kDACtC,8OAAC,wMAAY;wCACX,YAAY,MAAM;wCAClB,cAAc,MAAM;wCACpB,aAAa,MAAM;wCACnB,YAAY,MAAM;wCAClB,YAAY,MAAM;wCAClB,aAAa,MAAM;wCACnB,sBAAsB,CAAC,CAAC,MAAM;;;;;;kDAEhC,8OAAC;wCACC,MAAK;wCACL,UACE,YAAY,SAAS,IAAI,oBAAoB,SAAS;wCAExD,SAAS;kDAET,cAAA,8OAAC,wKAAW;4CACV,WACE,YAAY,SAAS,IAAI,oBAAoB,SAAS;4CAExD,aACE,oBAAoB,SAAS,GACzB,4BACA;4CAEN,YAAW;4CACX,aAAa;4CACb,cAAa;4CACb,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhC;uCAEe", "debugId": null}}]}